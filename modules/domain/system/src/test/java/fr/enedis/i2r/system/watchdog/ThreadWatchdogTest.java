package fr.enedis.i2r.system.watchdog;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.time.Duration;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ThreadWatchdogTest {

    @Mock
    private ScheduledExecutorService mockScheduler;

    @SuppressWarnings("unchecked")
    @Mock
    private ScheduledFuture<Object> mockFuture;

    private ThreadWatchdog threadWatchdog;
    private AtomicBoolean watchdogServiceStopped;

    @SuppressWarnings("unchecked")
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        threadWatchdog = new ThreadWatchdog(mockScheduler);
        watchdogServiceStopped = new AtomicBoolean(false);

        // Setup mock scheduler to return a mock future
        doReturn(mockFuture).when(mockScheduler)
            .scheduleAtFixedRate(any(Runnable.class), anyLong(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void testRegisterThreadStartsHeartbeatAndTimeoutChecker() {
        // Given
        String threadName = "TestThread";

        // When
        threadWatchdog.register(threadName);

        // Then
        assertTrue(threadWatchdog.getThreads().containsKey(threadName));
        verify(mockScheduler, times(2)).scheduleAtFixedRate(any(Runnable.class), anyLong(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void testSetWatchdogServiceStopper() {
        // Given
        Runnable stopper = () -> watchdogServiceStopped.set(true);

        // When
        threadWatchdog.setWatchdogServiceStopper(stopper);

        // Then - no exception should be thrown
        assertDoesNotThrow(() -> threadWatchdog.setWatchdogServiceStopper(stopper));
    }

    @Test
    void testUnregisterRemovesThreadAndCancelsHeartbeat() {
        // Given
        String threadName = "TestThread";
        threadWatchdog.register(threadName);

        // When
        threadWatchdog.unregister(threadName);

        // Then
        assertFalse(threadWatchdog.getThreads().containsKey(threadName));
        verify(mockFuture, atLeastOnce()).cancel(false);
    }

    @Test
    void testShutdownClearsAllThreadsAndCancelsFutures() {
        // Given
        threadWatchdog.register("Thread1");
        threadWatchdog.register("Thread2");

        // When
        threadWatchdog.shutdown();

        // Then
        assertTrue(threadWatchdog.getThreads().isEmpty());
        verify(mockFuture, atLeastOnce()).cancel(false);
        verify(mockScheduler).shutdown();
    }

    @Test
    void testSetHeartbeatInterval() {
        // Given
        Duration newInterval = Duration.ofSeconds(30);

        // When
        threadWatchdog.setHeartbeatInterval(newInterval);

        // Then - no exception should be thrown
        assertDoesNotThrow(() -> threadWatchdog.setHeartbeatInterval(newInterval));
    }

    @Test
    void testWatchdogServiceStopperIsCalledWhenConfigured() {
        // Given
        Runnable stopper = () -> watchdogServiceStopped.set(true);
        threadWatchdog.setWatchdogServiceStopper(stopper);

        // This test verifies that the stopper callback is properly stored
        // The actual timeout detection logic would require more complex mocking
        // of time and thread execution, which is better tested through integration tests

        // When/Then
        assertFalse(watchdogServiceStopped.get());
    }
}
