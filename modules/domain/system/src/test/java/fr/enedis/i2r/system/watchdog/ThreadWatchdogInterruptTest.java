package fr.enedis.i2r.system.watchdog;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Integration test to demonstrate that ThreadWatchdog stops updating timestamps
 * when a monitored thread is interrupted.
 */
class ThreadWatchdogInterruptTest {

    private static final Logger logger = LoggerFactory.getLogger(ThreadWatchdogInterruptTest.class);

    private ThreadWatchdog threadWatchdog;
    private ScheduledExecutorService testExecutor;
    private AtomicBoolean watchdogServiceStopped;

    @BeforeEach
    void setUp() {
        testExecutor = Executors.newScheduledThreadPool(2);
        threadWatchdog = new ThreadWatchdog(testExecutor);
        threadWatchdog.setHeartbeatInterval(Duration.ofMillis(100)); // Fast heartbeat for testing
        watchdogServiceStopped = new AtomicBoolean(false);
        threadWatchdog.setWatchdogServiceStopper(() -> watchdogServiceStopped.set(true));
    }

    @AfterEach
    void tearDown() {
        threadWatchdog.shutdown();
        testExecutor.shutdown();
    }

    @Test
    void testHeartbeatStopsWhenThreadIsInterrupted() throws InterruptedException {
        CountDownLatch threadStarted = new CountDownLatch(1);
        CountDownLatch threadCanProceed = new CountDownLatch(1);

        Thread testThread = new Thread(() -> {
            threadWatchdog.register("InterruptedThread");
            threadStarted.countDown();

            try {
                // Wait to be interrupted
                threadCanProceed.await();
            } catch (InterruptedException e) {
                logger.info("Test thread was interrupted as expected");
                Thread.currentThread().interrupt(); // Restore interrupt status
            }
        }, "TestInterruptedThread");

        // Start the test thread
        testThread.start();

        // Wait for thread to register
        assertTrue(threadStarted.await(1, TimeUnit.SECONDS));

        // Give some time for initial heartbeats
        Thread.sleep(250);

        // Get initial timestamp
        var initialTimestamp = threadWatchdog.getThreads().get("InterruptedThread");
        assertNotNull(initialTimestamp);

        // Interrupt the thread
        testThread.interrupt();

        // Give some time for heartbeat attempts after interruption
        Thread.sleep(250);

        // The thread should either be removed (if timeout was reached) or have old timestamp
        var finalTimestamp = threadWatchdog.getThreads().get("InterruptedThread");

        if (finalTimestamp != null) {
            // If still in map, timestamp should not have been updated significantly after interruption
            assertTrue(Duration.between(initialTimestamp, finalTimestamp).toMillis() < 150,
                "Timestamp should not be updated significantly after thread interruption");
        } else {
            // Thread was removed due to timeout detection, which is also correct behavior
            assertTrue(watchdogServiceStopped.get(), "WatchdogService should have been stopped");
        }

        testThread.join(1000);
    }

    @Test
    void testHeartbeatContinuesForNonInterruptedThread() throws InterruptedException {
        CountDownLatch threadStarted = new CountDownLatch(1);

        Thread testThread = new Thread(() -> {
            threadWatchdog.register("HealthyThread");
            threadStarted.countDown();

            try {
                // Just sleep without being interrupted
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "TestHealthyThread");

        // Start the test thread
        testThread.start();

        // Wait for thread to register
        assertTrue(threadStarted.await(1, TimeUnit.SECONDS));

        // Give some time for initial heartbeats
        Thread.sleep(150);

        // Get initial timestamp
        var initialTimestamp = threadWatchdog.getThreads().get("HealthyThread");
        assertNotNull(initialTimestamp);

        // Wait for more heartbeats
        Thread.sleep(250);

        // The timestamp should have been updated for healthy thread
        var finalTimestamp = threadWatchdog.getThreads().get("HealthyThread");

        assertTrue(finalTimestamp.isAfter(initialTimestamp),
            "Timestamp should be updated for healthy thread");

        testThread.join(1000);
    }
}
